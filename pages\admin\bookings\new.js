import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import Link from 'next/link';
import styles from '@/styles/admin/NewBooking.module.css';

/**
 * New Booking Creation Page
 * 
 * This page provides a form interface for creating new customer bookings.
 */
export default function NewBooking() {
  const router = useRouter();
  const [authenticated, setAuthenticated] = useState(false);
  const [loading, setLoading] = useState(true);

  // Check authentication
  useEffect(() => {
    const checkAuth = async () => {
      try {
        const isLoggedIn = localStorage.getItem('admin_logged_in') === 'true';
        setAuthenticated(isLoggedIn);
        if (!isLoggedIn) {
          router.push('/login');
          return;
        }
      } catch (error) {
        console.error('Error checking authentication:', error);
        setAuthenticated(false);
      } finally {
        setLoading(false);
      }
    };

    checkAuth();
  }, [router]);

  if (loading) {
    return (
      <div className={styles.loadingContainer}>
        <div className={styles.loadingSpinner}></div>
        <p>Loading...</p>
      </div>
    );
  }

  if (!authenticated) {
    return null; // Will redirect to login
  }

  return (
    <>
      <Head>
        <title>New Booking | Ocean Soul Sparkles Admin</title>
        <meta name="description" content="Create a new customer booking" />
      </Head>

      <div className={styles.newBookingContainer}>
        <header className={styles.header}>
          <h1 className={styles.title}>Create New Booking</h1>
          <div className={styles.headerActions}>
            <Link href="/admin/bookings" className={styles.backButton}>
              ← Back to Bookings
            </Link>
          </div>
        </header>

        <div className={styles.content}>
          <div className={styles.comingSoon}>
            <h2>New Booking Form</h2>
            <p>This page will contain a comprehensive booking creation form with:</p>
            <ul>
              <li>Customer selection/creation</li>
              <li>Service selection with pricing</li>
              <li>Date and time picker</li>
              <li>Artist assignment</li>
              <li>Special requirements and notes</li>
              <li>Payment processing integration</li>
            </ul>
            <div className={styles.actions}>
              <Link href="/admin/bookings" className={styles.backBtn}>
                Back to Bookings
              </Link>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
