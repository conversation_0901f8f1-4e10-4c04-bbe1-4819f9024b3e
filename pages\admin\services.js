import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import Link from 'next/link';
import styles from '@/styles/admin/Services.module.css';

/**
 * Services Management Page
 * 
 * This page provides a comprehensive interface for managing service catalog,
 * including pricing, categories, and service details.
 */
export default function ServicesManagement() {
  const router = useRouter();
  const [authenticated, setAuthenticated] = useState(false);
  const [loading, setLoading] = useState(true);
  const [services, setServices] = useState([]);
  const [filteredServices, setFilteredServices] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [sortBy, setSortBy] = useState('name');

  // Check authentication
  useEffect(() => {
    const checkAuth = async () => {
      try {
        const isLoggedIn = localStorage.getItem('admin_logged_in') === 'true';
        setAuthenticated(isLoggedIn);
        if (!isLoggedIn) {
          router.push('/login');
          return;
        }
        await loadServices();
      } catch (error) {
        console.error('Error checking authentication:', error);
        setAuthenticated(false);
      } finally {
        setLoading(false);
      }
    };

    checkAuth();
  }, [router]);

  // Load services from database
  const loadServices = async () => {
    try {
      // Mock services data - in production this would fetch from Supabase
      const mockServices = [
        {
          id: 1,
          name: 'Face Painting - Simple',
          category: 'Face Painting',
          price: 15,
          duration: 15,
          description: 'Basic face painting designs perfect for children',
          active: true,
          popularity: 85
        },
        {
          id: 2,
          name: 'Face Painting - Complex',
          category: 'Face Painting',
          price: 25,
          duration: 30,
          description: 'Intricate face painting designs with multiple colors',
          active: true,
          popularity: 92
        },
        {
          id: 3,
          name: 'Airbrush Tattoo - Small',
          category: 'Airbrush',
          price: 10,
          duration: 10,
          description: 'Small temporary airbrush tattoos',
          active: true,
          popularity: 78
        },
        {
          id: 4,
          name: 'Airbrush Tattoo - Large',
          category: 'Airbrush',
          price: 20,
          duration: 20,
          description: 'Large detailed temporary airbrush tattoos',
          active: true,
          popularity: 88
        },
        {
          id: 5,
          name: 'Hair Braiding - Simple',
          category: 'Braiding',
          price: 30,
          duration: 30,
          description: 'Basic braiding styles with optional colored extensions',
          active: true,
          popularity: 75
        },
        {
          id: 6,
          name: 'Hair Braiding - Complex',
          category: 'Braiding',
          price: 60,
          duration: 60,
          description: 'Intricate braiding patterns and festival styles',
          active: true,
          popularity: 95
        },
        {
          id: 7,
          name: 'Glitter Application',
          category: 'Glitter',
          price: 8,
          duration: 5,
          description: 'Eco-friendly biodegradable glitter application',
          active: true,
          popularity: 65
        },
        {
          id: 8,
          name: 'Body Art - Small',
          category: 'Body Art',
          price: 35,
          duration: 45,
          description: 'Small body art designs on arms or legs',
          active: true,
          popularity: 82
        }
      ];
      setServices(mockServices);
      setFilteredServices(mockServices);
    } catch (error) {
      console.error('Error loading services:', error);
    }
  };

  // Filter and sort services
  useEffect(() => {
    let filtered = services;

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(service =>
        service.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        service.description.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filter by category
    if (categoryFilter !== 'all') {
      filtered = filtered.filter(service => service.category === categoryFilter);
    }

    // Sort services
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'price':
          return a.price - b.price;
        case 'duration':
          return a.duration - b.duration;
        case 'popularity':
          return b.popularity - a.popularity;
        default:
          return a.name.localeCompare(b.name);
      }
    });

    setFilteredServices(filtered);
  }, [services, searchTerm, categoryFilter, sortBy]);

  // Get unique categories
  const categories = [...new Set(services.map(service => service.category))];

  // Toggle service active status
  const toggleServiceStatus = async (serviceId) => {
    try {
      setServices(prevServices =>
        prevServices.map(service =>
          service.id === serviceId
            ? { ...service, active: !service.active }
            : service
        )
      );
    } catch (error) {
      console.error('Error updating service status:', error);
    }
  };

  if (loading) {
    return (
      <div className={styles.loadingContainer}>
        <div className={styles.loadingSpinner}></div>
        <p>Loading services...</p>
      </div>
    );
  }

  if (!authenticated) {
    return null; // Will redirect to login
  }

  return (
    <>
      <Head>
        <title>Services Management | Ocean Soul Sparkles Admin</title>
        <meta name="description" content="Manage service catalog and pricing" />
      </Head>

      <div className={styles.servicesContainer}>
        <header className={styles.header}>
          <h1 className={styles.title}>Services Management</h1>
          <div className={styles.headerActions}>
            <Link href="/admin/services/new" className={styles.newServiceBtn}>
              + New Service
            </Link>
            <button 
              className={styles.backButton}
              onClick={() => router.push('/')}
            >
              ← Back to Dashboard
            </button>
          </div>
        </header>

        <div className={styles.controlsPanel}>
          <div className={styles.searchSection}>
            <input
              type="text"
              placeholder="Search services..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className={styles.searchInput}
            />
          </div>

          <div className={styles.filters}>
            <select
              value={categoryFilter}
              onChange={(e) => setCategoryFilter(e.target.value)}
              className={styles.categoryFilter}
            >
              <option value="all">All Categories</option>
              {categories.map(category => (
                <option key={category} value={category}>{category}</option>
              ))}
            </select>

            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className={styles.sortSelect}
            >
              <option value="name">Sort by Name</option>
              <option value="price">Sort by Price</option>
              <option value="duration">Sort by Duration</option>
              <option value="popularity">Sort by Popularity</option>
            </select>
          </div>
        </div>

        <div className={styles.servicesContent}>
          <div className={styles.statsCards}>
            <div className={styles.statCard}>
              <h3>Total Services</h3>
              <div className={styles.statValue}>{services.length}</div>
            </div>
            <div className={styles.statCard}>
              <h3>Active Services</h3>
              <div className={styles.statValue}>
                {services.filter(s => s.active).length}
              </div>
            </div>
            <div className={styles.statCard}>
              <h3>Categories</h3>
              <div className={styles.statValue}>{categories.length}</div>
            </div>
            <div className={styles.statCard}>
              <h3>Avg. Price</h3>
              <div className={styles.statValue}>
                ${Math.round(services.reduce((sum, s) => sum + s.price, 0) / services.length)}
              </div>
            </div>
          </div>

          {filteredServices.length === 0 ? (
            <div className={styles.emptyState}>
              <p>No services found matching your criteria.</p>
            </div>
          ) : (
            <div className={styles.servicesGrid}>
              {filteredServices.map(service => (
                <div key={service.id} className={styles.serviceCard}>
                  <div className={styles.serviceHeader}>
                    <div className={styles.serviceInfo}>
                      <h3>{service.name}</h3>
                      <span className={styles.category}>{service.category}</span>
                    </div>
                    <div className={`${styles.statusBadge} ${service.active ? styles.active : styles.inactive}`}>
                      {service.active ? 'Active' : 'Inactive'}
                    </div>
                  </div>

                  <div className={styles.serviceDetails}>
                    <p className={styles.description}>{service.description}</p>
                    
                    <div className={styles.serviceStats}>
                      <div className={styles.statItem}>
                        <span className={styles.statLabel}>Price:</span>
                        <span className={styles.statValue}>${service.price}</span>
                      </div>
                      <div className={styles.statItem}>
                        <span className={styles.statLabel}>Duration:</span>
                        <span className={styles.statValue}>{service.duration} min</span>
                      </div>
                      <div className={styles.statItem}>
                        <span className={styles.statLabel}>Popularity:</span>
                        <span className={styles.statValue}>{service.popularity}%</span>
                      </div>
                    </div>
                  </div>

                  <div className={styles.serviceActions}>
                    <Link href={`/admin/services/${service.id}`} className={styles.editBtn}>
                      Edit
                    </Link>
                    <button
                      onClick={() => toggleServiceStatus(service.id)}
                      className={`${styles.toggleBtn} ${service.active ? styles.deactivate : styles.activate}`}
                    >
                      {service.active ? 'Deactivate' : 'Activate'}
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </>
  );
}
