import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import styles from '@/styles/Auth.module.css';

export default function AdminLogin() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState('');

  const router = useRouter();

  // Check if already authenticated
  useEffect(() => {
    const isLoggedIn = localStorage.getItem('admin_logged_in') === 'true';
    if (isLoggedIn) {
      router.replace('/');
    }
  }, [router]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');

    if (!email || !password) {
      setError('Please fill in all fields');
      return;
    }

    setLoading(true);

    try {
      // Simple admin authentication - in production this should use proper auth
      if (email === '<EMAIL>' && password === 'admin123') {
        localStorage.setItem('admin_logged_in', 'true');
        router.push('/');
      } else {
        setError('Invalid email or password');
      }
    } catch (error) {
      console.error('Login error:', error);
      setError('Login failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <Head>
        <title>Admin Login - Ocean Soul Sparkles</title>
        <meta name="description" content="Sign in to the Ocean Soul Sparkles admin dashboard" />
        <meta name="robots" content="noindex, nofollow" />
      </Head>

      <div className={styles.authContainer}>
        <div className={styles.authCard}>
          <div className={styles.authHeader}>
            <h1>Admin Login</h1>
            <p>Sign in to access the admin dashboard</p>
          </div>

          {error && (
            <div className={styles.errorMessage}>
              {error}
            </div>
          )}

          <form onSubmit={handleSubmit} className={styles.authForm}>
            <div className={styles.formGroup}>
              <label htmlFor="email">Email Address</label>
              <input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Enter admin email"
                required
                disabled={loading}
                className={styles.formInput}
              />
            </div>

            <div className={styles.formGroup}>
              <label htmlFor="password">Password</label>
              <div className={styles.passwordInput}>
                <input
                  id="password"
                  type={showPassword ? 'text' : 'password'}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="Enter admin password"
                  required
                  disabled={loading}
                  className={styles.formInput}
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className={styles.passwordToggle}
                  disabled={loading}
                >
                  {showPassword ? '👁️' : '👁️‍🗨️'}
                </button>
              </div>
            </div>

            <button
              type="submit"
              disabled={loading}
              className={`${styles.authButton} ${loading ? styles.loading : ''}`}
            >
              {loading ? (
                <>
                  <span className={styles.buttonSpinner}></span>
                  Signing In...
                </>
              ) : (
                'Sign In to Admin'
              )}
            </button>
          </form>

          <div className={styles.adminInfo}>
            <div className={styles.divider}>
              <span>Demo Credentials</span>
            </div>
            <p className={styles.demoCredentials}>
              <strong>Email:</strong> <EMAIL><br />
              <strong>Password:</strong> admin123
            </p>
          </div>
        </div>
      </div>
    </>
  );
}
