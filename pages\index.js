import { useState, useEffect } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import { useRouter } from 'next/router';
import styles from '@/styles/admin/Dashboard.module.css';

/**
 * Admin Dashboard Page
 *
 * This page serves as the main admin dashboard with tabs for different management functions.
 */
export default function AdminDashboard() {
  const router = useRouter();
  const [authenticated, setAuthenticated] = useState(false);
  const [loading, setLoading] = useState(true);
  const [refreshKey, setRefreshKey] = useState(0);

  // Check authentication status
  useEffect(() => {
    const checkAuth = async () => {
      try {
        // For now, we'll use a simple check - in production this should use proper auth
        const isLoggedIn = localStorage.getItem('admin_logged_in') === 'true';
        setAuthenticated(isLoggedIn);
      } catch (error) {
        console.error('Error checking authentication:', error);
        setAuthenticated(false);
      } finally {
        setLoading(false);
      }
    };

    checkAuth();
  }, []);

  // Handle refresh
  const handleRefresh = () => {
    setRefreshKey(prevKey => prevKey + 1);
  };

  // Handle logout
  const handleLogout = () => {
    localStorage.removeItem('admin_logged_in');
    router.push('/login');
  };

  // Render loading state
  if (loading) {
    return (
      <div className={styles.loadingContainer}>
        <div className={styles.loadingSpinner}></div>
        <p>Loading dashboard...</p>
      </div>
    );
  }

  // Render login prompt if not authenticated
  if (!authenticated) {
    return (
      <div className={styles.authContainer}>
        <h1>Admin Dashboard</h1>
        <p>Please log in to access the admin dashboard.</p>
        <Link href="/login" className={styles.loginButton}>
          Log In
        </Link>
      </div>
    );
  }

  return (
    <>
      <Head>
        <title>Admin Dashboard | Ocean Soul Sparkles</title>
        <meta name="description" content="Admin dashboard for Ocean Soul Sparkles" />
      </Head>

      <div className={styles.dashboard}>
        <header className={styles.header}>
          <h1 className={styles.title}>Ocean Soul Sparkles Admin</h1>
          <div className={styles.userInfo}>
            <span className={styles.userEmail}>Admin User</span>
            <button
              className={styles.logoutButton}
              onClick={handleLogout}
            >
              Log Out
            </button>
          </div>
        </header>

        <nav className={styles.tabs}>
          <Link
            href="/"
            className={`${styles.tabButton} ${router.pathname === '/' ? styles.active : ''}`}
          >
            Overview
          </Link>
          <Link
            href="/admin/pos"
            className={`${styles.tabButton} ${router.pathname === '/admin/pos' ? styles.active : ''}`}
          >
            POS System
          </Link>
          <Link
            href="/admin/bookings"
            className={`${styles.tabButton} ${router.pathname === '/admin/bookings' ? styles.active : ''}`}
          >
            Bookings
          </Link>
          <Link
            href="/admin/customers"
            className={`${styles.tabButton} ${router.pathname === '/admin/customers' ? styles.active : ''}`}
          >
            Customers
          </Link>
          <Link
            href="/admin/services"
            className={`${styles.tabButton} ${router.pathname === '/admin/services' ? styles.active : ''}`}
          >
            Services
          </Link>
          <Link
            href="/admin/inventory"
            className={`${styles.tabButton} ${router.pathname === '/admin/inventory' ? styles.active : ''}`}
          >
            Inventory
          </Link>
          <Link
            href="/admin/artists"
            className={`${styles.tabButton} ${router.pathname === '/admin/artists' ? styles.active : ''}`}
          >
            Artists
          </Link>
        </nav>

        <main className={styles.tabContent}>
          <div className={styles.overviewTab}>
            <div className={styles.tabHeader}>
              <h2 className={styles.tabTitle}>Dashboard Overview</h2>
              <button
                className={styles.refreshButton}
                onClick={handleRefresh}
              >
                Refresh
              </button>
            </div>

            <div className={styles.statsGrid}>
              <div className={styles.statCard}>
                <h3>Today's Bookings</h3>
                <div className={styles.statValue}>12</div>
                <div className={styles.statChange}>+3 from yesterday</div>
              </div>
              <div className={styles.statCard}>
                <h3>Revenue Today</h3>
                <div className={styles.statValue}>$1,240</div>
                <div className={styles.statChange}>+15% from yesterday</div>
              </div>
              <div className={styles.statCard}>
                <h3>Active Customers</h3>
                <div className={styles.statValue}>847</div>
                <div className={styles.statChange}>+12 this week</div>
              </div>
              <div className={styles.statCard}>
                <h3>Inventory Alerts</h3>
                <div className={styles.statValue}>3</div>
                <div className={styles.statChange}>Low stock items</div>
              </div>
            </div>

            <div className={styles.quickActions}>
              <h3>Quick Actions</h3>
              <div className={styles.actionGrid}>
                <Link href="/admin/bookings/new" className={styles.actionCard}>
                  <h4>New Booking</h4>
                  <p>Create a new customer booking</p>
                </Link>
                <Link href="/admin/pos" className={styles.actionCard}>
                  <h4>Open POS</h4>
                  <p>Process payments and sales</p>
                </Link>
                <Link href="/admin/customers/new" className={styles.actionCard}>
                  <h4>Add Customer</h4>
                  <p>Register a new customer</p>
                </Link>
                <Link href="/admin/inventory" className={styles.actionCard}>
                  <h4>Check Inventory</h4>
                  <p>View stock levels and alerts</p>
                </Link>
              </div>
            </div>
          </div>


        </main>
      </div>
    </>
  );
}
