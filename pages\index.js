import { useState, useEffect } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import { useRouter } from 'next/router';
import styles from '@/styles/admin/Dashboard.module.css';

/**
 * Admin Dashboard Page
 *
 * This page serves as the main admin dashboard with tabs for different management functions.
 */
export default function AdminDashboard() {
  const router = useRouter();
  const [authenticated, setAuthenticated] = useState(false);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');
  const [refreshKey, setRefreshKey] = useState(0);

  // Check authentication status
  useEffect(() => {
    const checkAuth = async () => {
      try {
        // For now, we'll use a simple check - in production this should use proper auth
        const isLoggedIn = localStorage.getItem('admin_logged_in') === 'true';
        setAuthenticated(isLoggedIn);
      } catch (error) {
        console.error('Error checking authentication:', error);
        setAuthenticated(false);
      } finally {
        setLoading(false);
      }
    };

    checkAuth();
  }, []);

  // Handle refresh
  const handleRefresh = () => {
    setRefreshKey(prevKey => prevKey + 1);
  };

  // Handle logout
  const handleLogout = () => {
    localStorage.removeItem('admin_logged_in');
    router.push('/login');
  };

  // Render loading state
  if (loading) {
    return (
      <div className={styles.loadingContainer}>
        <div className={styles.loadingSpinner}></div>
        <p>Loading dashboard...</p>
      </div>
    );
  }

  // Render login prompt if not authenticated
  if (!authenticated) {
    return (
      <div className={styles.authContainer}>
        <h1>Admin Dashboard</h1>
        <p>Please log in to access the admin dashboard.</p>
        <Link href="/login" className={styles.loginButton}>
          Log In
        </Link>
      </div>
    );
  }

  return (
    <>
      <Head>
        <title>Admin Dashboard | Ocean Soul Sparkles</title>
        <meta name="description" content="Admin dashboard for Ocean Soul Sparkles" />
      </Head>

      <div className={styles.dashboard}>
        <header className={styles.header}>
          <h1 className={styles.title}>Ocean Soul Sparkles Admin</h1>
          <div className={styles.userInfo}>
            <span className={styles.userEmail}>Admin User</span>
            <button
              className={styles.logoutButton}
              onClick={handleLogout}
            >
              Log Out
            </button>
          </div>
        </header>

        <nav className={styles.tabs}>
          <button
            className={`${styles.tabButton} ${activeTab === 'overview' ? styles.active : ''}`}
            onClick={() => setActiveTab('overview')}
          >
            Overview
          </button>
          <button
            className={`${styles.tabButton} ${activeTab === 'pos' ? styles.active : ''}`}
            onClick={() => setActiveTab('pos')}
          >
            POS System
          </button>
          <button
            className={`${styles.tabButton} ${activeTab === 'bookings' ? styles.active : ''}`}
            onClick={() => setActiveTab('bookings')}
          >
            Bookings
          </button>
          <button
            className={`${styles.tabButton} ${activeTab === 'customers' ? styles.active : ''}`}
            onClick={() => setActiveTab('customers')}
          >
            Customers
          </button>
          <button
            className={`${styles.tabButton} ${activeTab === 'services' ? styles.active : ''}`}
            onClick={() => setActiveTab('services')}
          >
            Services
          </button>
          <button
            className={`${styles.tabButton} ${activeTab === 'inventory' ? styles.active : ''}`}
            onClick={() => setActiveTab('inventory')}
          >
            Inventory
          </button>
          <button
            className={`${styles.tabButton} ${activeTab === 'artists' ? styles.active : ''}`}
            onClick={() => setActiveTab('artists')}
          >
            Artists
          </button>
        </nav>

        <main className={styles.tabContent}>
          {activeTab === 'overview' && (
            <div className={styles.overviewTab}>
              <div className={styles.tabHeader}>
                <h2 className={styles.tabTitle}>Dashboard Overview</h2>
                <button
                  className={styles.refreshButton}
                  onClick={handleRefresh}
                >
                  Refresh
                </button>
              </div>

              <div className={styles.statsGrid}>
                <div className={styles.statCard}>
                  <h3>Today's Bookings</h3>
                  <div className={styles.statValue}>12</div>
                  <div className={styles.statChange}>+3 from yesterday</div>
                </div>
                <div className={styles.statCard}>
                  <h3>Revenue Today</h3>
                  <div className={styles.statValue}>$1,240</div>
                  <div className={styles.statChange}>+15% from yesterday</div>
                </div>
                <div className={styles.statCard}>
                  <h3>Active Customers</h3>
                  <div className={styles.statValue}>847</div>
                  <div className={styles.statChange}>+12 this week</div>
                </div>
                <div className={styles.statCard}>
                  <h3>Inventory Alerts</h3>
                  <div className={styles.statValue}>3</div>
                  <div className={styles.statChange}>Low stock items</div>
                </div>
              </div>

              <div className={styles.quickActions}>
                <h3>Quick Actions</h3>
                <div className={styles.actionGrid}>
                  <Link href="/admin/bookings/new" className={styles.actionCard}>
                    <h4>New Booking</h4>
                    <p>Create a new customer booking</p>
                  </Link>
                  <button
                    className={styles.actionCard}
                    onClick={() => setActiveTab('pos')}
                  >
                    <h4>Open POS</h4>
                    <p>Process payments and sales</p>
                  </button>
                  <Link href="/admin/customers/new" className={styles.actionCard}>
                    <h4>Add Customer</h4>
                    <p>Register a new customer</p>
                  </Link>
                  <button
                    className={styles.actionCard}
                    onClick={() => setActiveTab('inventory')}
                  >
                    <h4>Check Inventory</h4>
                    <p>View stock levels and alerts</p>
                  </button>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'pos' && (
            <div className={styles.posTab}>
              <div className={styles.tabHeader}>
                <h2 className={styles.tabTitle}>Point of Sale System</h2>
                <div className={styles.tabActions}>
                  <button className={styles.newButton}>
                    New Transaction
                  </button>
                </div>
              </div>
              <div className={styles.comingSoon}>
                <h3>POS System</h3>
                <p>Point of Sale functionality will be restored here.</p>
                <p>Features will include:</p>
                <ul>
                  <li>Service selection and pricing</li>
                  <li>Customer lookup and creation</li>
                  <li>Payment processing with Square</li>
                  <li>Receipt generation</li>
                  <li>Quick event mode for festivals</li>
                </ul>
              </div>
            </div>
          )}

          {activeTab === 'bookings' && (
            <div className={styles.bookingsTab}>
              <div className={styles.tabHeader}>
                <h2 className={styles.tabTitle}>Booking Management</h2>
                <div className={styles.tabActions}>
                  <Link href="/admin/bookings/new" className={styles.newButton}>
                    New Booking
                  </Link>
                </div>
              </div>
              <div className={styles.comingSoon}>
                <h3>Booking Management</h3>
                <p>Booking management functionality will be restored here.</p>
                <p>Features will include:</p>
                <ul>
                  <li>Calendar view of all bookings</li>
                  <li>Create, edit, and cancel bookings</li>
                  <li>Customer assignment and details</li>
                  <li>Service scheduling and artist assignment</li>
                  <li>Booking status tracking</li>
                </ul>
              </div>
            </div>
          )}

          {activeTab === 'customers' && (
            <div className={styles.customersTab}>
              <div className={styles.tabHeader}>
                <h2 className={styles.tabTitle}>Customer Management</h2>
                <div className={styles.tabActions}>
                  <Link href="/admin/customers/new" className={styles.newButton}>
                    New Customer
                  </Link>
                </div>
              </div>
              <div className={styles.comingSoon}>
                <h3>Customer Management</h3>
                <p>Customer management functionality will be restored here.</p>
                <p>Features will include:</p>
                <ul>
                  <li>Customer database and search</li>
                  <li>Customer profiles and history</li>
                  <li>Booking history and preferences</li>
                  <li>Contact information management</li>
                  <li>Customer analytics and insights</li>
                </ul>
              </div>
            </div>
          )}

          {activeTab === 'services' && (
            <div className={styles.servicesTab}>
              <div className={styles.tabHeader}>
                <h2 className={styles.tabTitle}>Service Management</h2>
                <div className={styles.tabActions}>
                  <Link href="/admin/services/new" className={styles.newButton}>
                    New Service
                  </Link>
                </div>
              </div>
              <div className={styles.comingSoon}>
                <h3>Service Management</h3>
                <p>Service management functionality will be restored here.</p>
                <p>Features will include:</p>
                <ul>
                  <li>Service catalog and pricing</li>
                  <li>Service categories and descriptions</li>
                  <li>Duration and availability settings</li>
                  <li>Artist specialization assignments</li>
                  <li>Service analytics and popularity</li>
                </ul>
              </div>
            </div>
          )}

          {activeTab === 'inventory' && (
            <div className={styles.inventoryTab}>
              <div className={styles.tabHeader}>
                <h2 className={styles.tabTitle}>Inventory Management</h2>
                <div className={styles.tabActions}>
                  <Link href="/admin/inventory/new" className={styles.newButton}>
                    Add Product
                  </Link>
                </div>
              </div>
              <div className={styles.comingSoon}>
                <h3>Inventory Management</h3>
                <p>Inventory management functionality will be restored here.</p>
                <p>Features will include:</p>
                <ul>
                  <li>Product catalog and stock levels</li>
                  <li>Low stock alerts and reordering</li>
                  <li>Supplier management</li>
                  <li>Purchase order tracking</li>
                  <li>Inventory analytics and reports</li>
                </ul>
              </div>
            </div>
          )}

          {activeTab === 'artists' && (
            <div className={styles.artistsTab}>
              <div className={styles.tabHeader}>
                <h2 className={styles.tabTitle}>Artist Management</h2>
                <div className={styles.tabActions}>
                  <Link href="/admin/artists/new" className={styles.newButton}>
                    Add Artist
                  </Link>
                </div>
              </div>
              <div className={styles.comingSoon}>
                <h3>Artist/Braider Management</h3>
                <p>Artist management functionality will be restored here.</p>
                <p>Features will include:</p>
                <ul>
                  <li>Artist profiles and specializations</li>
                  <li>Availability scheduling</li>
                  <li>Booking assignments</li>
                  <li>Performance tracking and analytics</li>
                  <li>Commission and payment management</li>
                </ul>
              </div>
            </div>
          )}
        </main>
      </div>
    </>
  );
}
