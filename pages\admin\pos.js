import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import styles from '@/styles/admin/POS.module.css';

/**
 * Point of Sale System
 * 
 * This page provides a comprehensive POS interface for processing transactions,
 * managing services, and handling customer interactions.
 */
export default function POSSystem() {
  const router = useRouter();
  const [authenticated, setAuthenticated] = useState(false);
  const [loading, setLoading] = useState(true);
  const [cart, setCart] = useState([]);
  const [customer, setCustomer] = useState(null);
  const [services, setServices] = useState([]);
  const [total, setTotal] = useState(0);
  const [paymentMethod, setPaymentMethod] = useState('card');
  const [isProcessing, setIsProcessing] = useState(false);

  // Check authentication
  useEffect(() => {
    const checkAuth = async () => {
      try {
        const isLoggedIn = localStorage.getItem('admin_logged_in') === 'true';
        setAuthenticated(isLoggedIn);
        if (!isLoggedIn) {
          router.push('/login');
          return;
        }
        await loadServices();
      } catch (error) {
        console.error('Error checking authentication:', error);
        setAuthenticated(false);
      } finally {
        setLoading(false);
      }
    };

    checkAuth();
  }, [router]);

  // Load services from database
  const loadServices = async () => {
    try {
      // Mock services data - in production this would fetch from Supabase
      const mockServices = [
        { id: 1, name: 'Face Painting - Simple', price: 15, duration: 15, category: 'Face Painting' },
        { id: 2, name: 'Face Painting - Complex', price: 25, duration: 30, category: 'Face Painting' },
        { id: 3, name: 'Airbrush Tattoo - Small', price: 10, duration: 10, category: 'Airbrush' },
        { id: 4, name: 'Airbrush Tattoo - Large', price: 20, duration: 20, category: 'Airbrush' },
        { id: 5, name: 'Hair Braiding - Simple', price: 30, duration: 30, category: 'Braiding' },
        { id: 6, name: 'Hair Braiding - Complex', price: 60, duration: 60, category: 'Braiding' },
        { id: 7, name: 'Glitter Application', price: 8, duration: 5, category: 'Glitter' },
        { id: 8, name: 'Body Art - Small', price: 35, duration: 45, category: 'Body Art' },
      ];
      setServices(mockServices);
    } catch (error) {
      console.error('Error loading services:', error);
    }
  };

  // Calculate total
  useEffect(() => {
    const newTotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    setTotal(newTotal);
  }, [cart]);

  // Add service to cart
  const addToCart = (service) => {
    setCart(prevCart => {
      const existingItem = prevCart.find(item => item.id === service.id);
      if (existingItem) {
        return prevCart.map(item =>
          item.id === service.id
            ? { ...item, quantity: item.quantity + 1 }
            : item
        );
      } else {
        return [...prevCart, { ...service, quantity: 1 }];
      }
    });
  };

  // Remove from cart
  const removeFromCart = (serviceId) => {
    setCart(prevCart => prevCart.filter(item => item.id !== serviceId));
  };

  // Update quantity
  const updateQuantity = (serviceId, newQuantity) => {
    if (newQuantity <= 0) {
      removeFromCart(serviceId);
      return;
    }
    setCart(prevCart =>
      prevCart.map(item =>
        item.id === serviceId
          ? { ...item, quantity: newQuantity }
          : item
      )
    );
  };

  // Process payment
  const processPayment = async () => {
    if (cart.length === 0) {
      alert('Cart is empty');
      return;
    }

    setIsProcessing(true);
    try {
      // Mock payment processing - in production this would integrate with Square
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Clear cart and show success
      setCart([]);
      setCustomer(null);
      alert('Payment processed successfully!');
    } catch (error) {
      console.error('Payment processing error:', error);
      alert('Payment failed. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  // Group services by category
  const servicesByCategory = services.reduce((acc, service) => {
    if (!acc[service.category]) {
      acc[service.category] = [];
    }
    acc[service.category].push(service);
    return acc;
  }, {});

  if (loading) {
    return (
      <div className={styles.loadingContainer}>
        <div className={styles.loadingSpinner}></div>
        <p>Loading POS System...</p>
      </div>
    );
  }

  if (!authenticated) {
    return null; // Will redirect to login
  }

  return (
    <>
      <Head>
        <title>POS System | Ocean Soul Sparkles Admin</title>
        <meta name="description" content="Point of Sale system for Ocean Soul Sparkles" />
      </Head>

      <div className={styles.posContainer}>
        <header className={styles.header}>
          <h1 className={styles.title}>Point of Sale</h1>
          <div className={styles.headerActions}>
            <button 
              className={styles.backButton}
              onClick={() => router.push('/')}
            >
              ← Back to Dashboard
            </button>
          </div>
        </header>

        <div className={styles.posLayout}>
          {/* Services Panel */}
          <div className={styles.servicesPanel}>
            <h2>Services</h2>
            <div className={styles.serviceCategories}>
              {Object.entries(servicesByCategory).map(([category, categoryServices]) => (
                <div key={category} className={styles.categorySection}>
                  <h3 className={styles.categoryTitle}>{category}</h3>
                  <div className={styles.serviceGrid}>
                    {categoryServices.map(service => (
                      <button
                        key={service.id}
                        className={styles.serviceCard}
                        onClick={() => addToCart(service)}
                      >
                        <div className={styles.serviceName}>{service.name}</div>
                        <div className={styles.servicePrice}>${service.price}</div>
                        <div className={styles.serviceDuration}>{service.duration} min</div>
                      </button>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Cart Panel */}
          <div className={styles.cartPanel}>
            <h2>Current Order</h2>
            
            {/* Customer Section */}
            <div className={styles.customerSection}>
              <h3>Customer</h3>
              {customer ? (
                <div className={styles.customerInfo}>
                  <p><strong>{customer.name}</strong></p>
                  <p>{customer.email}</p>
                  <button 
                    className={styles.changeCustomerBtn}
                    onClick={() => setCustomer(null)}
                  >
                    Change Customer
                  </button>
                </div>
              ) : (
                <div className={styles.customerActions}>
                  <button className={styles.addCustomerBtn}>
                    Add Customer
                  </button>
                  <button className={styles.walkInBtn}>
                    Walk-in Customer
                  </button>
                </div>
              )}
            </div>

            {/* Cart Items */}
            <div className={styles.cartItems}>
              {cart.length === 0 ? (
                <p className={styles.emptyCart}>No items in cart</p>
              ) : (
                cart.map(item => (
                  <div key={item.id} className={styles.cartItem}>
                    <div className={styles.itemInfo}>
                      <div className={styles.itemName}>{item.name}</div>
                      <div className={styles.itemPrice}>${item.price}</div>
                    </div>
                    <div className={styles.itemControls}>
                      <button 
                        className={styles.quantityBtn}
                        onClick={() => updateQuantity(item.id, item.quantity - 1)}
                      >
                        -
                      </button>
                      <span className={styles.quantity}>{item.quantity}</span>
                      <button 
                        className={styles.quantityBtn}
                        onClick={() => updateQuantity(item.id, item.quantity + 1)}
                      >
                        +
                      </button>
                      <button 
                        className={styles.removeBtn}
                        onClick={() => removeFromCart(item.id)}
                      >
                        ×
                      </button>
                    </div>
                  </div>
                ))
              )}
            </div>

            {/* Total and Payment */}
            <div className={styles.paymentSection}>
              <div className={styles.total}>
                <strong>Total: ${total.toFixed(2)}</strong>
              </div>
              
              <div className={styles.paymentMethods}>
                <label>
                  <input
                    type="radio"
                    value="card"
                    checked={paymentMethod === 'card'}
                    onChange={(e) => setPaymentMethod(e.target.value)}
                  />
                  Card Payment
                </label>
                <label>
                  <input
                    type="radio"
                    value="cash"
                    checked={paymentMethod === 'cash'}
                    onChange={(e) => setPaymentMethod(e.target.value)}
                  />
                  Cash
                </label>
              </div>

              <button 
                className={styles.processPaymentBtn}
                onClick={processPayment}
                disabled={cart.length === 0 || isProcessing}
              >
                {isProcessing ? 'Processing...' : `Process Payment ($${total.toFixed(2)})`}
              </button>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
