{"version": 2, "name": "oceansoulsparkles-admin", "alias": ["admin.oceansoulsparkles.com.au"], "regions": ["syd1"], "framework": "nextjs", "buildCommand": "npm run build", "outputDirectory": ".next", "installCommand": "npm ci", "devCommand": "npm run dev", "public": false, "functions": {"pages/api/**/*.js": {"maxDuration": 30}, "pages/api/auth/**/*.js": {"maxDuration": 10}, "pages/api/admin/**/*.js": {"maxDuration": 30}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(self), microphone=(self), geolocation=(self), payment=(self)"}, {"key": "Strict-Transport-Security", "value": "max-age=31536000; includeSubDomains; preload"}, {"key": "Content-Security-Policy", "value": "default-src 'self'; script-src 'self' 'unsafe-eval' 'unsafe-inline' https://js.squareup.com https://connect.squareup.com https://www.googletagmanager.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https: blob:; connect-src 'self' https://ndlgbcsbidyhxbpqzgqp.supabase.co https://connect.squareup.com https://pci-connect.squareup.com https://www.google-analytics.com; frame-src 'self' https://js.squareup.com; object-src 'none'; base-uri 'self'; form-action 'self';"}]}], "redirects": [{"source": "/admin", "destination": "/", "permanent": false}], "rewrites": [{"source": "/api/admin/:path*", "destination": "/api/admin/:path*"}, {"source": "/api/auth/:path*", "destination": "/api/auth/:path*"}], "crons": [{"path": "/api/admin/maintenance/cleanup", "schedule": "0 2 * * *"}, {"path": "/api/admin/backup/auto", "schedule": "0 */6 * * *"}]}