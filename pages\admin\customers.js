import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import Link from 'next/link';
import styles from '@/styles/admin/Customers.module.css';

/**
 * Customer Management Page
 * 
 * This page provides a comprehensive interface for managing customer data,
 * including search, profiles, booking history, and customer analytics.
 */
export default function CustomersManagement() {
  const router = useRouter();
  const [authenticated, setAuthenticated] = useState(false);
  const [loading, setLoading] = useState(true);
  const [customers, setCustomers] = useState([]);
  const [filteredCustomers, setFilteredCustomers] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState('name');
  const [sortOrder, setSortOrder] = useState('asc');
  const [selectedCustomer, setSelectedCustomer] = useState(null);

  // Check authentication
  useEffect(() => {
    const checkAuth = async () => {
      try {
        const isLoggedIn = localStorage.getItem('admin_logged_in') === 'true';
        setAuthenticated(isLoggedIn);
        if (!isLoggedIn) {
          router.push('/login');
          return;
        }
        await loadCustomers();
      } catch (error) {
        console.error('Error checking authentication:', error);
        setAuthenticated(false);
      } finally {
        setLoading(false);
      }
    };

    checkAuth();
  }, [router]);

  // Load customers from database
  const loadCustomers = async () => {
    try {
      // Mock customers data - in production this would fetch from Supabase
      const mockCustomers = [
        {
          id: 1,
          name: 'Sarah Johnson',
          email: '<EMAIL>',
          phone: '+61 400 123 456',
          created_at: '2024-01-15T10:00:00Z',
          total_bookings: 5,
          total_spent: 125,
          last_booking: '2024-06-10T14:00:00Z',
          status: 'active',
          notes: 'Prefers unicorn themes for face painting'
        },
        {
          id: 2,
          name: 'Mike Chen',
          email: '<EMAIL>',
          phone: '+61 400 234 567',
          created_at: '2024-02-20T09:30:00Z',
          total_bookings: 3,
          total_spent: 90,
          last_booking: '2024-06-14T14:00:00Z',
          status: 'active',
          notes: 'Regular customer for festival events'
        },
        {
          id: 3,
          name: 'Lisa Williams',
          email: '<EMAIL>',
          phone: '+61 400 345 678',
          created_at: '2024-03-10T16:15:00Z',
          total_bookings: 8,
          total_spent: 240,
          last_booking: '2024-06-15T11:00:00Z',
          status: 'vip',
          notes: 'VIP customer - loves intricate designs'
        },
        {
          id: 4,
          name: 'Tom Brown',
          email: '<EMAIL>',
          phone: '+61 400 456 789',
          created_at: '2024-04-05T11:20:00Z',
          total_bookings: 2,
          total_spent: 55,
          last_booking: '2024-05-20T10:30:00Z',
          status: 'active',
          notes: 'Interested in geometric patterns'
        },
        {
          id: 5,
          name: 'Emma Davis',
          email: '<EMAIL>',
          phone: '+61 400 567 890',
          created_at: '2024-05-12T14:45:00Z',
          total_bookings: 1,
          total_spent: 25,
          last_booking: '2024-05-12T15:00:00Z',
          status: 'new',
          notes: 'First-time customer'
        }
      ];
      setCustomers(mockCustomers);
      setFilteredCustomers(mockCustomers);
    } catch (error) {
      console.error('Error loading customers:', error);
    }
  };

  // Filter and sort customers
  useEffect(() => {
    let filtered = customers;

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(customer =>
        customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        customer.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        customer.phone.includes(searchTerm)
      );
    }

    // Sort customers
    filtered.sort((a, b) => {
      let aValue, bValue;
      
      switch (sortBy) {
        case 'name':
          aValue = a.name.toLowerCase();
          bValue = b.name.toLowerCase();
          break;
        case 'email':
          aValue = a.email.toLowerCase();
          bValue = b.email.toLowerCase();
          break;
        case 'total_bookings':
          aValue = a.total_bookings;
          bValue = b.total_bookings;
          break;
        case 'total_spent':
          aValue = a.total_spent;
          bValue = b.total_spent;
          break;
        case 'created_at':
          aValue = new Date(a.created_at);
          bValue = new Date(b.created_at);
          break;
        case 'last_booking':
          aValue = new Date(a.last_booking);
          bValue = new Date(b.last_booking);
          break;
        default:
          aValue = a.name.toLowerCase();
          bValue = b.name.toLowerCase();
      }

      if (sortOrder === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });

    setFilteredCustomers(filtered);
  }, [customers, searchTerm, sortBy, sortOrder]);

  // Get status badge class
  const getStatusBadgeClass = (status) => {
    switch (status) {
      case 'vip': return styles.statusVip;
      case 'active': return styles.statusActive;
      case 'new': return styles.statusNew;
      case 'inactive': return styles.statusInactive;
      default: return styles.statusDefault;
    }
  };

  // Format date
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString();
  };

  // Handle sort change
  const handleSort = (field) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('asc');
    }
  };

  if (loading) {
    return (
      <div className={styles.loadingContainer}>
        <div className={styles.loadingSpinner}></div>
        <p>Loading customers...</p>
      </div>
    );
  }

  if (!authenticated) {
    return null; // Will redirect to login
  }

  return (
    <>
      <Head>
        <title>Customer Management | Ocean Soul Sparkles Admin</title>
        <meta name="description" content="Manage customer database and profiles" />
      </Head>

      <div className={styles.customersContainer}>
        <header className={styles.header}>
          <h1 className={styles.title}>Customer Management</h1>
          <div className={styles.headerActions}>
            <Link href="/admin/customers/new" className={styles.newCustomerBtn}>
              + New Customer
            </Link>
            <button 
              className={styles.backButton}
              onClick={() => router.push('/')}
            >
              ← Back to Dashboard
            </button>
          </div>
        </header>

        <div className={styles.controlsPanel}>
          <div className={styles.searchSection}>
            <input
              type="text"
              placeholder="Search customers by name, email, or phone..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className={styles.searchInput}
            />
          </div>

          <div className={styles.sortSection}>
            <label>Sort by:</label>
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className={styles.sortSelect}
            >
              <option value="name">Name</option>
              <option value="email">Email</option>
              <option value="total_bookings">Total Bookings</option>
              <option value="total_spent">Total Spent</option>
              <option value="created_at">Join Date</option>
              <option value="last_booking">Last Booking</option>
            </select>
            <button
              onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
              className={styles.sortOrderBtn}
            >
              {sortOrder === 'asc' ? '↑' : '↓'}
            </button>
          </div>
        </div>

        <div className={styles.customersContent}>
          <div className={styles.customersList}>
            <div className={styles.customersHeader}>
              <div className={styles.statsCards}>
                <div className={styles.statCard}>
                  <h3>Total Customers</h3>
                  <div className={styles.statValue}>{customers.length}</div>
                </div>
                <div className={styles.statCard}>
                  <h3>VIP Customers</h3>
                  <div className={styles.statValue}>
                    {customers.filter(c => c.status === 'vip').length}
                  </div>
                </div>
                <div className={styles.statCard}>
                  <h3>New This Month</h3>
                  <div className={styles.statValue}>
                    {customers.filter(c => c.status === 'new').length}
                  </div>
                </div>
                <div className={styles.statCard}>
                  <h3>Total Revenue</h3>
                  <div className={styles.statValue}>
                    ${customers.reduce((sum, c) => sum + c.total_spent, 0)}
                  </div>
                </div>
              </div>
            </div>

            {filteredCustomers.length === 0 ? (
              <div className={styles.emptyState}>
                <p>No customers found matching your search criteria.</p>
              </div>
            ) : (
              <div className={styles.customersGrid}>
                {filteredCustomers.map(customer => (
                  <div key={customer.id} className={styles.customerCard}>
                    <div className={styles.customerHeader}>
                      <div className={styles.customerInfo}>
                        <h3>{customer.name}</h3>
                        <p>{customer.email}</p>
                        <p>{customer.phone}</p>
                      </div>
                      <div className={`${styles.statusBadge} ${getStatusBadgeClass(customer.status)}`}>
                        {customer.status}
                      </div>
                    </div>

                    <div className={styles.customerStats}>
                      <div className={styles.statItem}>
                        <span className={styles.statLabel}>Bookings:</span>
                        <span className={styles.statValue}>{customer.total_bookings}</span>
                      </div>
                      <div className={styles.statItem}>
                        <span className={styles.statLabel}>Total Spent:</span>
                        <span className={styles.statValue}>${customer.total_spent}</span>
                      </div>
                      <div className={styles.statItem}>
                        <span className={styles.statLabel}>Joined:</span>
                        <span className={styles.statValue}>{formatDate(customer.created_at)}</span>
                      </div>
                      <div className={styles.statItem}>
                        <span className={styles.statLabel}>Last Booking:</span>
                        <span className={styles.statValue}>{formatDate(customer.last_booking)}</span>
                      </div>
                    </div>

                    {customer.notes && (
                      <div className={styles.customerNotes}>
                        <strong>Notes:</strong> {customer.notes}
                      </div>
                    )}

                    <div className={styles.customerActions}>
                      <Link href={`/admin/customers/${customer.id}`} className={styles.viewBtn}>
                        View Profile
                      </Link>
                      <Link href={`/admin/bookings/new?customer=${customer.id}`} className={styles.bookBtn}>
                        New Booking
                      </Link>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
}
