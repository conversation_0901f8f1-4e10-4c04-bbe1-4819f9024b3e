import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import Link from 'next/link';
import styles from '@/styles/admin/Inventory.module.css';

/**
 * Inventory Management Page
 * 
 * This page provides a comprehensive interface for managing product inventory,
 * including stock levels, alerts, and product catalog.
 */
export default function InventoryManagement() {
  const router = useRouter();
  const [authenticated, setAuthenticated] = useState(false);
  const [loading, setLoading] = useState(true);
  const [inventory, setInventory] = useState([]);
  const [filteredInventory, setFilteredInventory] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [stockFilter, setStockFilter] = useState('all');
  const [sortBy, setSortBy] = useState('name');

  // Check authentication
  useEffect(() => {
    const checkAuth = async () => {
      try {
        const isLoggedIn = localStorage.getItem('admin_logged_in') === 'true';
        setAuthenticated(isLoggedIn);
        if (!isLoggedIn) {
          router.push('/login');
          return;
        }
        await loadInventory();
      } catch (error) {
        console.error('Error checking authentication:', error);
        setAuthenticated(false);
      } finally {
        setLoading(false);
      }
    };

    checkAuth();
  }, [router]);

  // Load inventory from database
  const loadInventory = async () => {
    try {
      // Mock inventory data - in production this would fetch from Supabase
      const mockInventory = [
        {
          id: 1,
          name: 'Face Paint - Red',
          category: 'Face Paint',
          current_stock: 25,
          min_stock: 10,
          max_stock: 50,
          unit_cost: 8.50,
          supplier: 'Art Supplies Co',
          last_restocked: '2024-06-01',
          status: 'in_stock'
        },
        {
          id: 2,
          name: 'Face Paint - Blue',
          category: 'Face Paint',
          current_stock: 8,
          min_stock: 10,
          max_stock: 50,
          unit_cost: 8.50,
          supplier: 'Art Supplies Co',
          last_restocked: '2024-05-28',
          status: 'low_stock'
        },
        {
          id: 3,
          name: 'Biodegradable Glitter - Gold',
          category: 'Glitter',
          current_stock: 15,
          min_stock: 5,
          max_stock: 30,
          unit_cost: 12.00,
          supplier: 'Eco Glitter Ltd',
          last_restocked: '2024-06-10',
          status: 'in_stock'
        },
        {
          id: 4,
          name: 'Airbrush Paint - Black',
          category: 'Airbrush',
          current_stock: 2,
          min_stock: 5,
          max_stock: 20,
          unit_cost: 15.00,
          supplier: 'Professional Art',
          last_restocked: '2024-05-15',
          status: 'critical'
        },
        {
          id: 5,
          name: 'Hair Extensions - Rainbow',
          category: 'Hair Accessories',
          current_stock: 12,
          min_stock: 8,
          max_stock: 25,
          unit_cost: 6.50,
          supplier: 'Hair Plus',
          last_restocked: '2024-06-08',
          status: 'in_stock'
        },
        {
          id: 6,
          name: 'Makeup Brushes Set',
          category: 'Tools',
          current_stock: 6,
          min_stock: 3,
          max_stock: 15,
          unit_cost: 25.00,
          supplier: 'Beauty Tools Inc',
          last_restocked: '2024-05-20',
          status: 'in_stock'
        },
        {
          id: 7,
          name: 'Temporary Tattoo Stencils',
          category: 'Stencils',
          current_stock: 0,
          min_stock: 10,
          max_stock: 50,
          unit_cost: 3.50,
          supplier: 'Stencil World',
          last_restocked: '2024-04-30',
          status: 'out_of_stock'
        },
        {
          id: 8,
          name: 'Sanitizing Wipes',
          category: 'Hygiene',
          current_stock: 45,
          min_stock: 20,
          max_stock: 100,
          unit_cost: 4.00,
          supplier: 'Health Supplies',
          last_restocked: '2024-06-12',
          status: 'in_stock'
        }
      ];
      setInventory(mockInventory);
      setFilteredInventory(mockInventory);
    } catch (error) {
      console.error('Error loading inventory:', error);
    }
  };

  // Filter and sort inventory
  useEffect(() => {
    let filtered = inventory;

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(item =>
        item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.supplier.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filter by category
    if (categoryFilter !== 'all') {
      filtered = filtered.filter(item => item.category === categoryFilter);
    }

    // Filter by stock status
    if (stockFilter !== 'all') {
      filtered = filtered.filter(item => item.status === stockFilter);
    }

    // Sort inventory
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'stock':
          return b.current_stock - a.current_stock;
        case 'cost':
          return b.unit_cost - a.unit_cost;
        case 'status':
          return a.status.localeCompare(b.status);
        default:
          return a.name.localeCompare(b.name);
      }
    });

    setFilteredInventory(filtered);
  }, [inventory, searchTerm, categoryFilter, stockFilter, sortBy]);

  // Get unique categories
  const categories = [...new Set(inventory.map(item => item.category))];

  // Get status badge class
  const getStatusBadgeClass = (status) => {
    switch (status) {
      case 'in_stock': return styles.statusInStock;
      case 'low_stock': return styles.statusLowStock;
      case 'critical': return styles.statusCritical;
      case 'out_of_stock': return styles.statusOutOfStock;
      default: return styles.statusDefault;
    }
  };

  // Get status display text
  const getStatusText = (status) => {
    switch (status) {
      case 'in_stock': return 'In Stock';
      case 'low_stock': return 'Low Stock';
      case 'critical': return 'Critical';
      case 'out_of_stock': return 'Out of Stock';
      default: return status;
    }
  };

  // Calculate inventory stats
  const stats = {
    totalItems: inventory.length,
    lowStock: inventory.filter(item => item.status === 'low_stock').length,
    critical: inventory.filter(item => item.status === 'critical').length,
    outOfStock: inventory.filter(item => item.status === 'out_of_stock').length,
    totalValue: inventory.reduce((sum, item) => sum + (item.current_stock * item.unit_cost), 0)
  };

  if (loading) {
    return (
      <div className={styles.loadingContainer}>
        <div className={styles.loadingSpinner}></div>
        <p>Loading inventory...</p>
      </div>
    );
  }

  if (!authenticated) {
    return null; // Will redirect to login
  }

  return (
    <>
      <Head>
        <title>Inventory Management | Ocean Soul Sparkles Admin</title>
        <meta name="description" content="Manage product inventory and stock levels" />
      </Head>

      <div className={styles.inventoryContainer}>
        <header className={styles.header}>
          <h1 className={styles.title}>Inventory Management</h1>
          <div className={styles.headerActions}>
            <Link href="/admin/inventory/new" className={styles.newItemBtn}>
              + Add Product
            </Link>
            <button 
              className={styles.backButton}
              onClick={() => router.push('/')}
            >
              ← Back to Dashboard
            </button>
          </div>
        </header>

        <div className={styles.controlsPanel}>
          <div className={styles.searchSection}>
            <input
              type="text"
              placeholder="Search products or suppliers..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className={styles.searchInput}
            />
          </div>

          <div className={styles.filters}>
            <select
              value={categoryFilter}
              onChange={(e) => setCategoryFilter(e.target.value)}
              className={styles.categoryFilter}
            >
              <option value="all">All Categories</option>
              {categories.map(category => (
                <option key={category} value={category}>{category}</option>
              ))}
            </select>

            <select
              value={stockFilter}
              onChange={(e) => setStockFilter(e.target.value)}
              className={styles.stockFilter}
            >
              <option value="all">All Stock Levels</option>
              <option value="in_stock">In Stock</option>
              <option value="low_stock">Low Stock</option>
              <option value="critical">Critical</option>
              <option value="out_of_stock">Out of Stock</option>
            </select>

            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className={styles.sortSelect}
            >
              <option value="name">Sort by Name</option>
              <option value="stock">Sort by Stock Level</option>
              <option value="cost">Sort by Cost</option>
              <option value="status">Sort by Status</option>
            </select>
          </div>
        </div>

        <div className={styles.inventoryContent}>
          <div className={styles.statsCards}>
            <div className={styles.statCard}>
              <h3>Total Items</h3>
              <div className={styles.statValue}>{stats.totalItems}</div>
            </div>
            <div className={styles.statCard}>
              <h3>Low Stock Alerts</h3>
              <div className={styles.statValue}>{stats.lowStock + stats.critical}</div>
            </div>
            <div className={styles.statCard}>
              <h3>Out of Stock</h3>
              <div className={styles.statValue}>{stats.outOfStock}</div>
            </div>
            <div className={styles.statCard}>
              <h3>Total Value</h3>
              <div className={styles.statValue}>${stats.totalValue.toFixed(2)}</div>
            </div>
          </div>

          {filteredInventory.length === 0 ? (
            <div className={styles.emptyState}>
              <p>No inventory items found matching your criteria.</p>
            </div>
          ) : (
            <div className={styles.inventoryGrid}>
              {filteredInventory.map(item => (
                <div key={item.id} className={styles.inventoryCard}>
                  <div className={styles.itemHeader}>
                    <div className={styles.itemInfo}>
                      <h3>{item.name}</h3>
                      <span className={styles.category}>{item.category}</span>
                    </div>
                    <div className={`${styles.statusBadge} ${getStatusBadgeClass(item.status)}`}>
                      {getStatusText(item.status)}
                    </div>
                  </div>

                  <div className={styles.itemDetails}>
                    <div className={styles.stockInfo}>
                      <div className={styles.stockLevel}>
                        <span className={styles.currentStock}>{item.current_stock}</span>
                        <span className={styles.stockRange}>/ {item.max_stock} units</span>
                      </div>
                      <div className={styles.stockBar}>
                        <div 
                          className={styles.stockFill}
                          style={{ 
                            width: `${(item.current_stock / item.max_stock) * 100}%`,
                            backgroundColor: item.status === 'critical' ? '#ef4444' : 
                                           item.status === 'low_stock' ? '#f59e0b' : '#10b981'
                          }}
                        ></div>
                      </div>
                    </div>

                    <div className={styles.itemStats}>
                      <div className={styles.statItem}>
                        <span className={styles.statLabel}>Unit Cost:</span>
                        <span className={styles.statValue}>${item.unit_cost}</span>
                      </div>
                      <div className={styles.statItem}>
                        <span className={styles.statLabel}>Supplier:</span>
                        <span className={styles.statValue}>{item.supplier}</span>
                      </div>
                      <div className={styles.statItem}>
                        <span className={styles.statLabel}>Last Restocked:</span>
                        <span className={styles.statValue}>
                          {new Date(item.last_restocked).toLocaleDateString()}
                        </span>
                      </div>
                      <div className={styles.statItem}>
                        <span className={styles.statLabel}>Total Value:</span>
                        <span className={styles.statValue}>
                          ${(item.current_stock * item.unit_cost).toFixed(2)}
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className={styles.itemActions}>
                    <Link href={`/admin/inventory/${item.id}`} className={styles.editBtn}>
                      Edit
                    </Link>
                    <button className={styles.restockBtn}>
                      Restock
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </>
  );
}
